apply plugin: 'com.android.library'
apply from: rootProject.file("base_module.gradle")


android {
    sourceSets { main { jni.srcDirs = ['src/main/jni', 'src/main/jniLibs/'] } }
    namespace 'com.rtmart.common'
}

dependencies {
    api fileTree(dir: "libs", include: ["*.jar",'*.aar'])
    api network
    api androidx
    api RT

    api libs.eventbus
    api libs.glide
    api libs.flexbox
    kapt libs.glideCompiler
    api libs.mmkv
//    api libs.bugly
    api libs.smartRefreshLayout
    api libs.multitype
    api libs.immersionbar
    api libs.pickerView
    api libs.zxing
    api libs.commonCompress
    api libs.tukaani
    api libs.junitKtx
    api libs.autoSize
    api libs.TagTextView
    api libs.protobuf
    api libs.fastjson
    api('com.journeyapps:zxing-android-embedded:4.3.0') { transitive = false }
    //广告sdk
//    if (project.hasProperty('AD_SDK_ONLINE') && Integer.parseInt(AD_SDK_ONLINE) == 1) {
//        println("_____________ad_online")
//        api libs.adplayerOnline
//    } else {
//        println("_____________ad_beta")
//        api libs.adplayerBeta
//    }

    api libs.bcprov_jdk15on
    api libs.bcpkix_jdk15on
    api libs.KotlinReflect

    api libs.lombok
    kapt libs.lombok
    api (libs.pos){
        exclude group: 'org.projectlombok', module: 'lombok'
    }
    androidTestApi(libs.junit)
}

