ext {

    moudle = [
            "app_base" : true,
            "compose_base" : true
    ]
    applicationId = [
            "debug" : "com.rtmart.rtpos.debug",
            "release": "com.rtmart.rtpos",
            "selfpos_debug": "com.rtmart.rtselfpos.debug",
            "selfpos_release": "com.rtmart.rtselfpos",
            "mobpos_debug": "com.rtmart.rtmobpos.debug",
            "mobpos_release": "com.rtmart.rtmobpos"
    ]
    //version配置
    versions = [
            "javaVersion"        : JavaVersion.VERSION_17,
            "kotlinVersion"      : "1.6.0",
            "buildGradleVersion" : "7.0.4",
            "junitVersion"       : "4.12",
            "composeVersoin"     :  "1.0.5",
            "navigationVersion" : "2.5.3",
            "navigationComposeVersion": "2.4.0-alpha06",
            "constraintlayoutComposeVersion":"1.0.0-beta02",
            "lifecycleVersion" : "2.5.1",
            "accompanistVersion" : "0.16.0",
            "kotlinCompilerVersion": "1.5.31",
            "autoServiceVersion" : '1.0',
            "kotlinpoetVersion" : '1.10.1',
            "rxpermissionsVersion" : "0.12",
            "coroutinesVersion" : "1.5.31",
            "immersionbarVersion" : "3.2.2",
            "roomVersion"       :  "2.5.2"
    ]
    //google

    google = [
            "swiperefresh" : "com.google.accompanist:accompanist-swiperefresh:$versions.accompanistVersion",
            "insets" : "com.google.accompanist:accompanist-insets:$versions.accompanistVersion",
            "systemuicontroller" : "com.google.accompanist:accompanist-systemuicontroller:$versions.accompanistVersion"
    ]

    //android开发版本配置
    androidVersion = [
            compileSdkVersion: 33,
            buildToolsVersion: "29.0.2",
            applicationId    : "com.rtmart.rtpos",
            selfappId : "com.rtmart.rtselfpos",
            minSdkVersion    : 22,
            targetSdkVersion : 33,
            versionCode      : 1,
            versionName      : "1.0",
    ]
    androidxs = [
            "multidex":"androidx.multidex:multidex:2.0.0",
            "core" :'androidx.core:core-ktx:1.8.0',
            "lifecycle-viewmodel" : "androidx.lifecycle:lifecycle-viewmodel-ktx:$versions.lifecycleVersion",
            "lifecycle-livedata":"androidx.lifecycle:lifecycle-livedata-ktx:$versions.lifecycleVersion",
            "appcompat" :'androidx.appcompat:appcompat:1.5.0',
            "constraintlayout": 'androidx.constraintlayout:constraintlayout:2.1.3',
            "kotlinx-coroutines-core":'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.0',
            "kotlinx-coroutines-android": 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.0',
            "androidx-lifecycle": "androidx.lifecycle:lifecycle-extensions:2.2.0",
            "kotlin":"org.jetbrains.kotlin:kotlin-stdlib:$versions.kotlinVersion",
            "material-ui":"com.google.android.material:material:1.5.0",
            "navigation-fragment" :"androidx.navigation:navigation-fragment-ktx:$versions.navigationVersion",
            "navigation-ui": "androidx.navigation:navigation-ui-ktx:$versions.navigationVersion",
            "viewpager2":"androidx.viewpager2:viewpager2:1.0.0",
            "drawerlayout":"androidx.drawerlayout:drawerlayout:1.1.1",
            "workmanager":"androidx.work:work-runtime-ktx:2.7.1"
    ]
    signConfigs = [
            "storeFile"     : "../rtmart",
            "storePassword" : "123456",
            "keyAlias"      : "rtmart",
            "keyPassword"   : "123456"
    ]

    //网络
    networks = [
            "gson" :'com.google.code.gson:gson:2.8.5'
    ]

    //依赖RT配置
    RTS = [
            "debugManager"      : "com.rtmart.lib:debug-manager:1.0.5",
            "rtprogress"        : "com.feiniu.app.lib:rtprogress:1.0.21",
            "rtlogger"          : "com.rtmart.lib:rtlogger:1.0.6",
            "rtdownloadfile"    : "com.rtmart.lib:rtdownloadfile:1.1.3",
            "taglabel"          : "com.rtmart.lib:taglabel:1.0.4",
//            "rtagent"           : "com.rtmart.lib:rtagent:1.0.8",
            "rtnetwork"         : "com.rtmart.lib:rtretrofitlib-light:1.0.7",
            "utils"             : "com.feiniu.app.lib:utils:1.2.7"
    ]

    logins = [
            "pdaloginaccount" :'com.feiniu.app.lib:pdaloginaccount:1.1.0',
            "pdaloginedp": 'com.feiniu.app.lib:pdaloginedp:1.1.1'
    ]
    //compose 依赖配置
    compose =[
            "ui"                    : "androidx.compose.ui:ui:$versions.composeVersoin",
            "activity"              : "androidx.activity:activity-compose:1.3.1",
            "navigationRuntime"     : "androidx.navigation:navigation-runtime-ktx:2.3.5",
            "material"              : "androidx.compose.material:material:$versions.composeVersoin",
            "uiPreview"             : "androidx.compose.ui:ui-tooling-preview:$versions.composeVersoin",
            "foundation"            : "androidx.compose.foundation:foundation:$versions.composeVersoin",
            "layout"                : "androidx.compose.foundation:foundation-layout:$versions.composeVersoin",
            "materialIconsExtended" : "androidx.compose.material:material-icons-extended:$versions.composeVersoin",
            "runtime"               : "androidx.compose.runtime:runtime:$versions.composeVersoin",
            "runtimeLivedata"       : "androidx.compose.runtime:runtime-livedata:$versions.composeVersoin",
            "tooling"               : "androidx.compose.ui:ui-tooling:$versions.composeVersoin",
            "test"                  : "androidx.compose.ui:ui-test:$versions.composeVersoin",
            "uiTest"                : "androidx.compose.ui:ui-test-junit4:$versions.composeVersoin",
            "uiUtil"                : "androidx.compose.ui:ui-util:$versions.composeVersoin",
            "viewBinding"           : "androidx.compose.ui:ui-viewbinding:$versions.composeVersoin",
            "viewmodel"             : "androidx.lifecycle:lifecycle-viewmodel-compose:1.0.0-alpha07",
            "livedata"              : "androidx.compose.runtime:runtime-livedata:$versions.composeVersoin",
            "navigation"            : "androidx.navigation:navigation-compose:$versions.navigationComposeVersion",
            "constraintlayout"      : "androidx.constraintlayout:constraintlayout-compose:$versions.constraintlayoutComposeVersion"
    ]

    //依赖其它第三方配置
    libs = [
            "junit"                                : "junit:junit:4.+",
            "junitKtx"                            : "androidx.test.ext:junit-ktx:1.1.3",
            "material"                             : "com.google.android.material:material:1.3.0",
            //rxjava
            "rxjava"                               : "io.reactivex.rxjava2:rxjava:2.2.3",
            "rxandroid"                            : "io.reactivex.rxjava2:rxandroid:2.1.0",
            //rx系列与View生命周期同步
            "rxlifecycle"                          : "com.trello.rxlifecycle2:rxlifecycle:2.2.2",
            "rxlifecycleComponents"                : "com.trello.rxlifecycle2:rxlifecycle-components:2.2.2",
            //rxbinding
            "rxbinding"                            : "com.jakewharton.rxbinding2:rxbinding:2.1.1",
            //rx 6.0权限请求
            "rxpermissions"                        : "com.github.tbruyelle:rxpermissions:0.12",
            //network
            "okhttp"                               : "com.squareup.okhttp3:okhttp:3.10.0",
            "retrofit"                             : "com.squareup.retrofit2:retrofit:2.4.0",
            "converterGson"                        : "com.squareup.retrofit2:converter-gson:2.4.0",
            "adapterRxjava"                        : "com.squareup.retrofit2:adapter-rxjava2:2.4.0",
            "loggingInterceptor"                   : "com.squareup.okhttp3:logging-interceptor:3.9.1",
            "LoggingInterceptor"                   : "com.github.ihsanbal:LoggingInterceptor:3.1.0-rc5",

            //glide图片加载
            "glide"                                : "com.github.bumptech.glide:glide:4.14.2",
            "glideCompiler"                        : "com.github.bumptech.glide:compiler:4.14.2",
            //json解析
            "gson"                                 : "com.google.code.gson:gson:2.8.9",
            "jsonParse"                            : "com.beust:klaxon:5.5",
            "zxing"                                : "com.github.jenly1314:zxing-lite:2.2.1",

            //Google AAC
            "lifecycleExtensions"                  : "android.arch.lifecycle:extensions:1.1.1",
            "lifecycleCompiler"                    : "android.arch.lifecycle:compiler:1.1.1",
            //阿里路由框架
            "arouterApi"                           : "com.alibaba:arouter-api:1.5.2",
            "arouterCompiler"                      : "com.alibaba:arouter-compiler:1.2.2",

            "eventbus"                             : "org.greenrobot:eventbus:3.1.1",
            "dagger"                               : "com.google.dagger:dagger:2.13",
            "daggerCompiler"                       : "com.google.dagger:dagger-compiler:2.13",
            "stetho"                               : "com.facebook.stetho:stetho:1.3.1",
            "smartRefreshLayout"                   : "com.scwang.smartrefresh:SmartRefreshLayout:1.0.5.1",
            "multiImageSelector"                   : "com.github.lovetuzitong:MultiImageSelector:1.2",
            "okgo"                                 : "com.lzy.net:okgo:3.0.4",
            "routerCompiler"                       : "com.rtmart.lib:router-compiler:1.0.0",
            // dataStore
            "datastore"                            : "androidx.datastore:datastore-preferences:1.0.0",
            "autoServiceVersion"                   : "com.google.auto.service:auto-service:$versions.autoServiceVersion",
            "kotlinpoet"                           : "com.squareup:kotlinpoet:$versions.kotlinpoetVersion",
            "immersionbar"                         : "com.geyifeng.immersionbar:immersionbar:$versions.immersionbarVersion",
            "util"                                 : "com.blankj:utilcodex:1.31.1",
            "mmkv"                                 : "com.tencent:mmkv:1.3.2",
            "refreshLayout"                        : "io.github.scwang90:refresh-layout-kernel:2.0.5",
            "multitype"                            : "com.drakeet.multitype:multitype:4.3.0",
            "bugly"                                : "com.tencent.bugly:crashreport:4.1.9",
            "leakcanary"                           : "com.squareup.leakcanary:leakcanary-android:2.10",
            // 个推
            "gtsdk"                                : "com.getui:gtsdk:3.2.10.0",
            "gtc"                                  : "com.getui:gtc:3.1.9.0",
            "hwp"                                  : "com.getui.opt:hwp:3.1.1",
            "xmp"                                  : "com.getui.opt:xmp:3.3.0",
            "oppo"                                 : "com.assist-v3:oppo:3.3.0",
            "vivo"                                 : "com.assist-v3:vivo:3.1.1",
            "mzp"                                  : "com.getui.opt:mzp:3.2.2",
            "honor"                                : "com.getui.opt:honor:3.3.0",
            //pick view
            "pickerView"                           : "com.contrarywind:Android-PickerView:4.1.9",
            //compress
            "commonCompress"                       : "org.apache.commons:commons-compress:1.9",
            "tukaani"                              : "org.tukaani:xz:1.8",
            //mqtt
            "mqttv4"                               : "androidx.legacy:legacy-support-v4:1.0.0",
            "mqttService"                          : "com.github.hannesa2:paho.mqtt.android:3.3.5",
            "mqttClient"                           : "org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.5",
            "flexbox"                              : "com.google.android:flexbox:2.0.1",
            "zip"                                  : "org.zeroturnaround:zt-zip:1.13",
            "roomCompiler"                         : "androidx.room:room-compiler:$versions.roomVersion",
            "autoSize"                             : "com.github.JessYanCoding:AndroidAutoSize:v1.2.1",
            "TagTextView"                          : "com.github.ChinaLike:TagTextView:0.2.6", //带标签的textview

            // 广告
            "adplayerBeta"                         :"com.kiwi.adsdk:adplayer:3.5.7-SNAPSHOT",
            "adplayerOnline"                       :"com.kiwi.adsdk:adplayer:1.0.1",

            //SM4加密
            "bcprov_jdk15on"                        :"org.bouncycastle:bcprov-jdk15on:1.68",
            "bcpkix_jdk15on"                        :"org.bouncycastle:bcpkix-jdk15on:1.68",

            // 反射
            "KotlinReflect"                          :"org.jetbrains.kotlin:kotlin-reflect:$versions.kotlinVersion",

            //protobuf
            "protobuf":"com.google.protobuf:protobuf-java:3.5.1@jar",
            //fastjson
            "fastjson"  :"com.alibaba:fastjson:1.1.45@jar",

            //lua 脚本
            "lua" :"me.chenhe:android-lua:1.1.1",
            //添加pos sdk
            "pos"               :"com.feiniu.pos:pos:3.0.2.001-RELEASE",
            "lombok"            :"org.projectlombok:lombok:1.18.28"
    ]

    applifecycle = [
            "appCommon"                  : "com.github.hufeiyang:Common:1.0.2",
            "appApi"                     : "com.github.hufeiyang.Android-AppLifecycleMgr:applifecycle-api:1.0.4",
            "appCompiler"                : "com.github.hufeiyang.Android-AppLifecycleMgr:applifecycle-compiler:1.0.4"
    ]

    dbs = [
            "room"         :"androidx.room:room-ktx:$versions.roomVersion",
            "roomPaging"   :"androidx.room:room-paging:$versions.roomVersion",
    ]

    RT = RTS.values()
    db = dbs.values()
    network = networks.values()
    androidx = androidxs.values()
}



