apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'kotlin-parcelize'
apply plugin: 'androidx.navigation.safeargs.kotlin'

android {
    compileSdk androidVersion.compileSdkVersion


    signingConfigs {

        debug {
            storeFile file(signConfigs.storeFile)
            storePassword signConfigs.storePassword
            keyAlias signConfigs.keyAlias
            keyPassword signConfigs.keyPassword
        }
        release {
            storeFile file(signConfigs.storeFile)
            storePassword signConfigs.storePassword
            keyAlias signConfigs.keyAlias
            keyPassword signConfigs.keyPassword
        }
    }

    defaultConfig {
        minSdk androidVersion.minSdkVersion
        targetSdk androidVersion.targetSdkVersion
        versionCode androidVersion.versionCode
        versionName androidVersion.versionName
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"


        //jenkins打包配置1
        if (project.hasProperty('MQTT_ENABLE_PARA')) {
            buildConfigField "int", "MQTT_ENABLE", MQTT_ENABLE_PARA
        } else {
            buildConfigField "int", "MQTT_ENABLE", "1"
        }
        //jenkins打包配置2
        if (project.hasProperty('VERSION_CODE_PARA')) {
            versionCode Integer.parseInt(VERSION_CODE_PARA)
        } else {
            versionCode 999
        }
        //jenkins打包配置3
        if (project.hasProperty('VERSION_NAME_PARA')) {
            versionName VERSION_NAME_PARA
        } else if (project.hasProperty('VERSION_CODE_PARA')) {
            def nameFromCode = VERSION_CODE_PARA.substring(0, 1) + "." + VERSION_CODE_PARA.substring(1, 3) + "." + VERSION_CODE_PARA.substring(3)
            versionName nameFromCode
        } else if(project.hasProperty('VERSION_BETA_CODE_PARA')) {
            def nameFromCode = VERSION_BETA_CODE_PARA.substring(0, 1) + "." + VERSION_BETA_CODE_PARA.substring(1, 2) + "." + VERSION_BETA_CODE_PARA.substring(2,3)+"."+VERSION_BETA_CODE_PARA.substring(3,4)
            versionName nameFromCode
        }else{
            versionName "9.99.99"
        }

        multiDexEnabled true

        //配置当前支持的cpu架构
        ndk {
            abiFilters "arm64-v8a", "armeabi-v7a", "x86", "x86_64"
        }
        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
                arg("AROUTER_GENERATE_DOC","enable")
            }
        }


    }

    buildFeatures {
        buildConfig true
    }

    buildTypes {
        debug {
            minifyEnabled true
            //proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            consumerProguardFiles 'consumer-rules.pro'
            signingConfig signingConfigs.debug
        }
        release {
            minifyEnabled true
            //proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            consumerProguardFiles 'consumer-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    dataBinding {
        enabled true
    }

    buildFeatures {
        viewBinding true
    }
    compileOptions {
        sourceCompatibility versions.javaVersion
        targetCompatibility versions.javaVersion
    }
    kotlinOptions {
        jvmTarget = versions.javaVersion
    }

    kapt {
        arguments {
            arg("AROUTER_MODULE_NAME", project.getName())
            arg("AROUTER_GENERATE_DOC","enable")
        }
    }

    configurations {
        implementation {
            canBeResolved = true
        }
    }

    lintOptions{
        checkReleaseBuilds false
        abortOnError false
    }

}


dependencies {
    api libs.arouterApi
    kapt libs.arouterCompiler
    api(platform("org.jetbrains.kotlin:kotlin-bom:1.8.0"))
}