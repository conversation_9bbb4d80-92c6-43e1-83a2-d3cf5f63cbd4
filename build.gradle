// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: "config.gradle"
buildscript {
    repositories {
        maven {url 'https://maven.aliyun.com/repository/google'}
        maven { setUrl("https://maven.aliyun.com/repository/central") }
        maven { setUrl("https://maven.aliyun.com/repository/jcenter") }
        maven {url 'https://maven.aliyun.com/repository/gradle-plugin'}
        maven {url 'https://maven.aliyun.com/repository/public'}
        maven {url 'https://maven.aliyun.com/repository/mapr-public'}
        maven {url 'https://maven.aliyun.com/repository/central'}
        maven {url 'https://maven.aliyun.com/repository/releases'}
        maven {url 'https://jitpack.io'}
        maven {
            allowInsecureProtocol true
            url 'http://maven.fn.com/nexus/content/groups/public/'
        }

        maven {
            url "http://*************:8088/repository/rtrepo/"
            allowInsecureProtocol true
        }
        maven {
            allowInsecureProtocol true
            url 'http://maven-mgt.fn.com/repository/android-group/'
        }
        maven {
            //广告sdk
            url 'https://kiwi-remote-maven.pkg.coding.net/repository/AdSdkDemo/nexus3/'
        }
        maven {
            url "https://mvn.getui.com/nexus/content/repositories/releases/"
        }
        mavenCentral()
        google()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.4'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.22"
        classpath "androidx.navigation:navigation-safe-args-gradle-plugin:2.5.3"
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}