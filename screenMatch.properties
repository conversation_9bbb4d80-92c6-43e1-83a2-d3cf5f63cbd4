############################################################################
# Start with '#' is annotate.                                              #
# In front of '=' is key, cannot be modified.                              #
# More information to visit:                                               #
#   http://blog.csdn.net/fesdgasdgasdg/article/details/52325590            #
#   http://download.csdn.net/detail/fesdgasdgasdg/9913744                  #
#   https://github.com/mengzhinan/PhoneScreenMatch                         #
############################################################################
#
# You need to refresh or reopen the project every time you modify the configuration,
# or you can't get the latest configuration parameters.
#
#############################################################################
#
# Base dp value for screen match. Cut the screen into [base_dp] parts.
# Data type is double. System default value is 360.
# I advise you not to modify the value, be careful !!!!!!!!! _^_  *_*
base_dp=360
# Also need to match the phone screen of [match_dp].
# If you have another dp values.
# System default values is 240,320,384,392,400,410,411,480,533,592,600,640,662,720,768,800,811,820,960,961,1024,1280,1365
match_dp= 160,240,320,384,392,400,410,411,480,533,592,600,640,662,720,768,800,811,820,960,961,1024,1280,1365
# If you not wanna to match dp values above. Write some above values here, append value with "," .
# For example: 811,961,1365
ignore_dp=
# They're not android module name. If has moreï¿½ï¿½split with , Symbol.
# If you set, it will not show in SelectDialog.
# If you have, write here and append value with "," .
# For example: testLibrary,commonModule
# System default values is .gradle, gradle, .idea, build, .git
ignore_module_name=
# Use which module under the values/dimen.xml file to do the base file,
# and generated dimen.xml file store in this module?
# Default value is 'app'.
match_module=app
# Don't show select dialog again when use this plugin.
# System screen match will use the last selected module name or default module name.
# You can give value true or false. Default value is false.
not_show_dialog=false
# Do you want to generate the default example dimens.xml file?
# In path of .../projectName/screenMatch_example_dimens.xml, It does not affect your project code.
# You can give value true or false. Default value is false.
not_create_default_dimens=false
# Does the font scale the same size as the DP? May not be accuracy.
# You can give value true or false. Default value is true. Also need scaled.
is_match_font_sp=true
# Do you want to create values-wXXXdp folder or values-swXXXdp folder ?
# I suggest you create values-swXXXdp folder,
# because I had a problem when I was working on the horizontal screen adapter.
# values-swXXXdp folder can solve my problem.
# If you want create values-swXXXdp folder, set "create_values_sw_folder=true",
# otherwise set "create_values_sw_folder=true".
# Default values is true.
create_values_sw_folder=true
