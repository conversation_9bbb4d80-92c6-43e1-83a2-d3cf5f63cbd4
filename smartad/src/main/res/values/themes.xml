<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->

    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:statusBarColor" tools:ignore="NewApi">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animations.ActivityAnimation</item>
    </style>
    <style name="Theme.RTPos" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:statusBarColor" tools:ignore="NewApi">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animations.ActivityAnimation</item>
    </style>

    <style name="Theme.RTPos.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>


    <style name="FullScreen" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentNavigation" tools:ignore="NewApi">true</item>
        <!--解决部分手机隐藏状态栏顶部出现小黑条的问题-->
        <item name="android:windowLayoutInDisplayCutoutMode" tools:ignore="NewApi">shortEdges</item>
        <!--解决白屏问题-->
        <!--Android 5.x开始需要把颜色设置透明，否则导航栏会呈现系统默认的浅灰色-->
        <item name="android:statusBarColor" tools:ignore="NewApi">#00000000</item>
<!--        <item name="android:windowBackground">@drawable/ic_splash_bg</item>-->
    </style>

    <style name="Theme.RTPos.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="Theme.RTPos.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />
</resources>