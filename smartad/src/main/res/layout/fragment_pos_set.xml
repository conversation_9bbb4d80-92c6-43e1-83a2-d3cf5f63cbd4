<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:windowSoftInputMode="adjustPan">

    <TextView
        android:id="@+id/tvEdpLogin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_13"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@drawable/shape_logout_btn"
        android:paddingStart="@dimen/dp_13"
        android:paddingTop="@dimen/dp_6"
        android:paddingEnd="@dimen/dp_13"
        android:paddingBottom="@dimen/dp_6"
        android:text="退出登录"
        android:textColor="@color/mediumGray"
        android:textSize="@dimen/sp_10"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvPageTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:text="POS设置"
            android:textColor="@color/blue_2F54EB"
            android:textSize="@dimen/sp_18"
            android:textStyle="bold"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvErrorMsg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:text="机台号已存在，请重新设置"
            android:textColor="@color/red_E50113"
            android:textSize="@dimen/sp_18"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintVertical_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clTitle"
        app:layout_constraintBottom_toTopOf="@+id/tvSave">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clPosInfoSet"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_167"
            android:layout_marginTop="@dimen/dp_7">

            <TextView
                android:id="@+id/tvRed1"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_30"
                android:gravity="center"
                android:text="*"
                android:textColor="@color/red_E50113"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toStartOf="@+id/tvPosStoreTip"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvPosStoreTip"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_30"
                android:gravity="center_vertical"
                android:text="POS设备所属门店："
                android:textColor="@color/lightBlack"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toStartOf="@+id/lvPosStore"
                app:layout_constraintStart_toEndOf="@+id/tvRed1"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/lvPosStore"
                android:layout_width="@dimen/dp_200"
                android:layout_height="@dimen/dp_30"
                android:layout_marginBottom="@dimen/dp_162"
                android:background="@drawable/login_shape_search_text_view_background"
                app:layout_constraintStart_toEndOf="@+id/tvPosStoreTip"
                app:layout_constraintTop_toTopOf="parent">

                <EditText
                    android:id="@+id/edtStoreName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_6"
                    android:layout_marginTop="@dimen/dp_7"
                    android:layout_marginBottom="@dimen/dp_7"
                    android:layout_weight="1"
                    android:background="@null"
                    android:ems="10"
                    android:gravity="center_vertical"
                    android:hint="请选择门店"
                    android:imeOptions="flagNoFullscreen"
                    android:inputType="text|none"
                    android:textColor="@color/lightBlack"
                    android:textColorHint="@color/gray_999"
                    android:textCursorDrawable="@drawable/shape_et_cursor"
                    android:textSize="@dimen/sp_12" />

                <ImageButton
                    android:id="@+id/imgArrowDown1"
                    android:layout_width="@dimen/dp_14"
                    android:layout_height="@dimen/dp_14"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="@dimen/dp_13"
                    android:background="@drawable/arrow_down"
                    android:scaleType="fitCenter" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvPosDeviceTypeRed"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_30"
                android:gravity="center"
                android:text="*"
                android:textColor="@color/red_E50113"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toStartOf="@+id/tvPosDeviceTypeTip"
                app:layout_constraintTop_toTopOf="@+id/tvPosDeviceTypeTip" />

            <TextView
                android:id="@+id/tvPosDeviceTypeTip"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_30"
                android:gravity="center_vertical"
                android:text="POS设备类型："
                android:textColor="@color/lightBlack"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toEndOf="@+id/tvPosStoreTip"
                app:layout_constraintTop_toTopOf="@+id/lvPosDeviceType" />

            <LinearLayout
                android:id="@+id/lvPosDeviceType"
                android:layout_width="@dimen/dp_200"
                android:layout_height="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_5"
                android:background="@drawable/shape_login_pos_type_edit_text"
                app:layout_constraintEnd_toEndOf="@+id/lvPosStore"
                app:layout_constraintTop_toBottomOf="@+id/lvPosStore">

                <TextView
                    android:id="@+id/tvPosDeviceType"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp_9"
                    android:layout_marginTop="@dimen/dp_7"
                    android:layout_marginBottom="@dimen/dp_7"
                    android:layout_weight="1"
                    android:background="@null"
                    android:ems="10"
                    android:gravity="center_vertical"
                    android:hint="人工POS"
                    android:inputType="text|none"
                    android:text="人工POS"
                    android:textColor="@color/mediumGray"
                    android:textColorHint="@color/gray_999"
                    android:textSize="@dimen/sp_12" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvPosTypeRed"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_17"
                android:gravity="center"
                android:text="*"
                android:textColor="@color/red_E50113"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toStartOf="@+id/tvPosTypeTip"
                app:layout_constraintTop_toTopOf="@+id/tvPosTypeTip" />

            <TextView
                android:id="@+id/tvPosTypeTip"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_17"
                android:gravity="center_vertical"
                android:text="POS种类："
                android:textColor="@color/lightBlack"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toStartOf="@+id/rgPosType"
                app:layout_constraintTop_toTopOf="@+id/rgPosType" />

            <RadioGroup
                android:id="@+id/rgPosType"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_17"
                android:layout_marginTop="@dimen/dp_7"
                android:orientation="horizontal"
                app:layout_constraintStart_toEndOf="@+id/tvPosMachineNumberTip"
                app:layout_constraintTop_toBottomOf="@+id/lvPosDeviceType">

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/radioButtonPosType1"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_17"
                    android:button="@drawable/custom_radio_button"
                    android:buttonTint="@null"
                    android:gravity="center"
                    android:paddingStart="@dimen/dp_3"
                    android:text="收银线机台"
                    android:textColor="@color/lightBlack"
                    android:textSize="@dimen/sp_12"
                    app:buttonTint="@null" />

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/radioButtonPosType2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_14"
                    android:button="@drawable/custom_radio_button"
                    android:buttonTint="@null"
                    android:gravity="center"
                    android:paddingStart="@dimen/dp_3"
                    android:text="专柜机台"
                    android:textColor="@color/lightBlack"
                    android:textSize="@dimen/sp_12"
                    app:buttonTint="@null" />

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/radioButtonPosType3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_14"
                    android:button="@drawable/custom_radio_button"
                    android:buttonTint="@null"
                    android:gravity="center"
                    android:paddingStart="@dimen/dp_3"
                    android:text="场外机台"
                    android:textColor="@color/lightBlack"
                    android:textSize="@dimen/sp_12"
                    app:buttonTint="@null" />

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/radioButtonPosType4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_14"
                    android:button="@drawable/custom_radio_button"
                    android:buttonTint="@null"
                    android:gravity="center"
                    android:paddingStart="@dimen/dp_3"
                    android:text="特殊机台"
                    android:textColor="@color/lightBlack"
                    android:textSize="@dimen/sp_12"
                    app:buttonTint="@null" />

            </RadioGroup>

            <TextView
                android:id="@+id/tvPosMachineNumberRed"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_30"
                android:gravity="center_vertical"
                android:text="*"
                android:textColor="@color/red_E50113"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toStartOf="@+id/tvPosMachineNumberTip"
                app:layout_constraintTop_toTopOf="@+id/tvPosMachineNumberTip" />

            <TextView
                android:id="@+id/tvPosMachineNumberTip"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_30"
                android:gravity="center_vertical"
                android:text="POS机台号："
                android:textColor="@color/lightBlack"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toEndOf="@+id/tvPosStoreTip"
                app:layout_constraintTop_toTopOf="@+id/lvPosMachineNumber" />

            <LinearLayout
                android:id="@+id/lvPosMachineNumber"
                android:layout_width="@dimen/dp_200"
                android:layout_height="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_7"
                android:background="@drawable/login_shape_search_text_view_background"
                app:layout_constraintEnd_toEndOf="@+id/lvPosStore"
                app:layout_constraintTop_toBottomOf="@+id/rgPosType">

                <EditText
                    android:id="@+id/edtPosMachineNumber"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp_9"
                    android:layout_marginTop="@dimen/dp_7"
                    android:layout_marginBottom="@dimen/dp_7"
                    android:layout_weight="1"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="三位数字，001~999之间"
                    android:imeOptions="flagNoFullscreen"
                    android:inputType="number|none"
                    android:maxLength="3"
                    android:textColor="@color/lightBlack"
                    android:textColorHint="@color/gray_999"
                    android:textCursorDrawable="@drawable/shape_et_cursor"
                    android:textSize="@dimen/sp_12" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvPosDeviceNameRed"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_30"
                android:gravity="center_vertical"
                android:text="*"
                android:textColor="@color/red_E50113"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toStartOf="@+id/tvPosDeviceNameTip"
                app:layout_constraintTop_toTopOf="@+id/lvPosDeviceName" />

            <TextView
                android:id="@+id/tvPosDeviceNameTip"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_30"
                android:gravity="center_vertical"
                android:text="POS设备名称："
                android:textColor="@color/lightBlack"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toEndOf="@+id/tvPosStoreTip"
                app:layout_constraintTop_toTopOf="@+id/lvPosDeviceName" />

            <LinearLayout
                android:id="@+id/lvPosDeviceName"
                android:layout_width="@dimen/dp_200"
                android:layout_height="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_5"
                android:background="@drawable/shape_login_pos_type_edit_text"
                app:layout_constraintStart_toEndOf="@+id/tvPosDeviceNameTip"
                app:layout_constraintTop_toBottomOf="@+id/lvPosMachineNumber">

                <EditText
                    android:id="@+id/edtPosDeviceName"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp_9"
                    android:layout_marginTop="@dimen/dp_7"
                    android:layout_marginBottom="@dimen/dp_7"
                    android:layout_weight="1"
                    android:background="@null"
                    android:enabled="false"
                    android:gravity="center_vertical"
                    android:hint="请输入POS设备名称"
                    android:imeOptions="flagNoFullscreen"
                    android:inputType="text|none"
                    android:scrollbars="horizontal"
                    android:textColor="@color/mediumGray"
                    android:textColorHint="@color/gray_999"
                    android:textCursorDrawable="@drawable/shape_et_cursor"
                    android:textSize="@dimen/sp_12" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvSetAutoShutdownRed"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_17"
                android:gravity="center_vertical"
                android:text="*"
                android:textColor="@color/red_E50113"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toStartOf="@+id/tvSetAutoShutdown"
                app:layout_constraintTop_toTopOf="@+id/rgSetAutoShutdown" />

            <TextView
                android:id="@+id/tvSetAutoShutdown"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_17"
                android:gravity="center_vertical"
                android:text="POS开启自动关机："
                android:textColor="@color/lightBlack"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toEndOf="@+id/tvPosStoreTip"
                app:layout_constraintTop_toTopOf="@+id/rgSetAutoShutdown" />


            <RadioGroup
                android:id="@+id/rgSetAutoShutdown"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_17"
                android:layout_marginTop="@dimen/dp_7"
                android:orientation="horizontal"
                app:layout_constraintStart_toEndOf="@+id/tvSetAutoShutdown"
                app:layout_constraintTop_toBottomOf="@+id/lvPosDeviceName">

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/radioButtonOpenAutoShutdown"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:button="@drawable/custom_radio_button"
                    android:buttonTint="@null"
                    android:checked="true"
                    android:gravity="center"
                    android:paddingStart="@dimen/dp_3"
                    android:text="开启"
                    android:textColor="@color/lightBlack"
                    android:textSize="@dimen/sp_12"
                    app:buttonTint="@null" />

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/radioButtonCloseAutoShutdown"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_14"
                    android:button="@drawable/custom_radio_button"
                    android:buttonTint="@null"
                    android:gravity="center"
                    android:paddingStart="@dimen/dp_3"
                    android:text="不开启"
                    android:textColor="@color/lightBlack"
                    android:textSize="@dimen/sp_12"
                    app:buttonTint="@null" />
            </RadioGroup>

            <TextView
                android:id="@+id/tvPosAutomaticShutDownRed"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_30"
                android:gravity="center_vertical"
                android:text="*"
                android:textColor="@color/red_E50113"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toStartOf="@+id/tvPosAutomaticShutDownTip"
                app:layout_constraintTop_toTopOf="@+id/tvPosAutomaticShutDownTime" />

            <TextView
                android:id="@+id/tvPosAutomaticShutDownTip"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_30"
                android:gravity="center_vertical"
                android:text="POS自动关机时间："
                android:textColor="@color/lightBlack"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toEndOf="@+id/tvPosStoreTip"
                app:layout_constraintTop_toTopOf="@+id/tvPosAutomaticShutDownTime" />

            <TextView
                android:id="@+id/tvPosAutomaticShutDownTime"
                android:layout_width="@dimen/dp_200"
                android:layout_height="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_7"
                android:background="@drawable/login_shape_search_text_view_background"
                android:gravity="center_vertical"
                android:hint="请选择自动关机时间"
                android:inputType="text|none"
                android:paddingStart="@dimen/dp_9"
                android:paddingRight="@dimen/dp_13"
                android:text="23:00"
                android:textColor="@color/lightBlack"
                android:textColorHint="@color/gray_999"
                android:textCursorDrawable="@drawable/shape_et_cursor"
                android:textSize="@dimen/sp_12"
                app:drawableEndCompat="@drawable/bitmap_arrow_down"
                app:layout_constraintStart_toEndOf="@+id/tvPosAutomaticShutDownTip"
                app:layout_constraintTop_toBottomOf="@+id/rgSetAutoShutdown" />

            <TextView
                android:id="@+id/tvOffSiteExchangeRed"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_17"
                android:gravity="center_vertical"
                android:text="*"
                android:textColor="@color/red_E50113"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toStartOf="@+id/tvOffSiteExchangeTip"
                app:layout_constraintTop_toTopOf="@+id/tvOffSiteExchangeTip" />

            <TextView
                android:id="@+id/tvOffSiteExchangeTip"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_17"
                android:gravity="center_vertical"
                android:text="POS开启场外换购："
                android:textColor="@color/lightBlack"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toEndOf="@+id/tvPosStoreTip"
                app:layout_constraintTop_toTopOf="@+id/rgOffSiteExchange" />

            <RadioGroup
                android:id="@+id/rgOffSiteExchange"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_17"
                android:layout_marginTop="@dimen/dp_7"
                android:orientation="horizontal"
                app:layout_constraintStart_toEndOf="@+id/tvOffSiteExchangeTip"
                app:layout_constraintTop_toBottomOf="@+id/tvPosAutomaticShutDownTime">

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/radioButtonOffSiteExchange1"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_17"
                    android:button="@drawable/custom_radio_button"
                    android:buttonTint="@null"
                    android:checked="true"
                    android:gravity="center"
                    android:paddingStart="@dimen/dp_3"
                    android:text="不开启"
                    android:textColor="@color/lightBlack"
                    android:textSize="@dimen/sp_12"
                    app:buttonTint="@null" />

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/radioButtonOffSiteExchange2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_14"
                    android:button="@drawable/custom_radio_button"
                    android:buttonTint="@null"
                    android:gravity="center"
                    android:paddingStart="@dimen/dp_3"
                    android:text="开启"
                    android:textColor="@color/lightBlack"
                    android:textSize="@dimen/sp_12"
                    app:buttonTint="@null" />
            </RadioGroup>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clPosStoreList"
                android:layout_width="@dimen/dp_200"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_popupwindow"
                android:maxHeight="@dimen/dp_163"
                android:minHeight="@dimen/dp_50"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="@+id/lvPosStore"
                app:layout_constraintTop_toBottomOf="@+id/lvPosStore">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvPosStore"
                    android:layout_width="@dimen/dp_200"
                    android:layout_height="match_parent"
                    android:paddingTop="@dimen/dp_3"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:itemCount="5" />

                <TextView
                    android:id="@+id/emptyView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="无匹配项"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tvSave"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_34"
        android:layout_marginTop="@dimen/dp_22"
        android:layout_marginBottom="@dimen/dp_14"
        android:background="@drawable/selector_btn_common_radius_17"
        android:enabled="false"
        android:paddingStart="@dimen/dp_101"
        android:paddingTop="@dimen/dp_7"
        android:paddingEnd="@dimen/dp_101"
        android:paddingBottom="@dimen/dp_7"
        android:text="保存"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_15"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clKeyBoard"
        android:layout_width="@dimen/dp_260"
        android:layout_height="match_parent"
        android:layout_gravity="right"
        android:background="@drawable/bg_dialog_translucence"
        android:visibility="gone"
        android:windowSoftInputMode="adjustPan"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/ivClose"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginRight="@dimen/dp_10"
            android:src="@drawable/ic_close_24"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:id="@+id/flKeyboard"
            android:layout_width="@dimen/dp_224"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_44"
            android:background="@drawable/shape_bg_radius_8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivClose">

            <com.rtmart.common.ui.keyboard.RTKeyBoardNumber
                android:id="@+id/keyboardNumber"
                android:layout_width="@dimen/dp_200"
                android:layout_height="@dimen/dp_133"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/dp_28"
                android:layout_marginTop="@dimen/dp_12"
                android:layout_marginBottom="@dimen/dp_12"
                android:visibility="gone"
                app:keyBoard_textSize="@dimen/sp_14"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />


        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
