<?xml version="1.0" encoding="utf-8"?>
<!--suppress NavigationFile -->
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    app:startDestination="@id/RTEDPLoginFragment"
    android:id="@+id/nav_login.xml">

    <action
        android:id="@+id/action_to_RTEDPLoginFragment"
        app:destination="@id/RTEDPLoginFragment" />

    <action
        android:id="@+id/action_to_RTPosSetFragment"
        app:destination="@id/RTPosSetFragment" />
<!--    FIXME 这里要改成广告点位设置页-->


    <fragment
        android:id="@+id/RTEDPLoginFragment"
        android:name="com.rtmart.smartad.login.fragment.RTEDPLoginFragment"
        android:label="RTEDPLoginFragment"
        tools:layout="@layout/fragment_edplogin"/>

    <fragment
        android:id="@+id/RTPosSetFragment"
        android:name="com.rtmart.smartad.login.fragment.RTEDPLoginFragment"
        android:label="RTPosSetFragment">
<!--fixme 这里要改成广告点位设置页-->
        <argument
            android:name="stores"
            app:argType="com.rtmart.smartad.core.model.StoreInfo[]" />

    </fragment>

</navigation>