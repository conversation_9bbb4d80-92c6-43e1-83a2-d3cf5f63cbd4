plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-android'
}

apply from: rootProject.file("base_module.gradle")

android {

    defaultConfig {
        applicationId "com.rtmart.smartad"
    }

    namespace "com.rtmart.smartad"
    sourceSets {
        main {
            jni.srcDirs = ['src/main/jni', 'src/main/jniLibs/']
            aidl.srcDirs = ['src/main/aidl']
        }
    }

    android.applicationVariants.all { variant ->
        variant.outputs.all {
            if (variant.buildType.name == 'release') {
                outputFileName = "smartad.apk"
            } else if (variant.buildType.name == 'debug') {
                outputFileName = "smartad-debug.apk"
            }
        }
    }

    buildTypes {
        debug {
//            applicationIdSuffix ".debug"
            // fixme debug阶段暂时关闭R8混淆
            minifyEnabled false
            debuggable true
            zipAlignEnabled true // 开启Zip压缩优化
            // fixme debug阶段暂时关闭
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            debuggable false
            zipAlignEnabled true // 开启Zip压缩优化
            shrinkResources true
            signingConfig signingConfigs.release
        }
    }
}


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])
    implementation project(path: ':common')
//    implementation project(':core')
    api androidx
    api libs.eventbus
    debugImplementation libs.leakcanary
}