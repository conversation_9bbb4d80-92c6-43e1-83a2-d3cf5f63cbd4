# RTAds SDK 使用文档

## 一、简介

RTAds 是一个轻量级广告集成 SDK，支持多种广告形式适用于 Android 应用开发。本 SDK 提供了统一的接口调用方式，简化广告接入流程，并兼容主流广告平台。

---

## 二、环境要求

- Android API Level ≥ 21（Android 5.0 及以上）
- Kotlin 或 Java 支持
- Gradle 构建系统
- Target SDK Version ≥ 34

---

## 三、集成方式

### 1. 添加依赖

在你的 [build.gradle](file:///Users/<USER>/Documents/source/android/POS/build.gradle) 文件中添加 SDK 依赖：

## 四,快速开始

1.在Application中初始化SDK
```kotlin
val config = RTAdConfig.Builder()
.deviceId("device_001")
.storeId("store_888")
.width("1080")
.height("1920")
.ipAddress("***********")
.macAddress("00:1A:2B:3C:4D:5E")
.model("11")
.posNum("221")
.logEnabled(true)
.build()
RTAds.init(this, config,object : IInitCallback {
override fun success() {
println("初始化成功")
}

            override fun fail(code: Int, msg: String?) {
                println("初始化失败")
            }
        })
```

2. 使用SDK


--- 注意使用容器让广告去播放
RTAds.registerContainerView(adContainer)

```kotlin
   val adContainer = findViewById<FrameLayout>(R.id.banner)
   RTAds.registerContainerView(adContainer)
   RTAds.addAdStatusCallback(object : RTAdStatusCallback {
   override fun onAdStatus(hasAd: Boolean) {
   println("hasAd:${hasAd}")
   if(hasAd){
   println("start...")
   adContainer.visibility = View.VISIBLE
   //线上ui
   }else{
   //隐藏
    adContainer.visibility = View.GONE
     }
    }

        })
   }

```