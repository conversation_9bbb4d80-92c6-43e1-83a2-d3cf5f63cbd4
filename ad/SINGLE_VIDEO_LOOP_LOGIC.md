# 单视频循环播放逻辑说明

## 🔍 问题发现

您发现了一个重要的逻辑漏洞：当只有一个视频时，`handleItemCompleted()` 方法调用了 `scheduleNextItem()`，但 `scheduleNextItem()` 对视频类型不做任何处理，导致视频无法循环播放。

## 🐛 原始问题代码

```kotlin
// handleItemCompleted() 中
if (currentItem.type == 1) { // 视频
    Log.d(TAG, "只有一个视频，重新播放")
    viewPager.setCurrentItem(0, false)
    scheduleNextItem() // ❌ 问题：scheduleNextItem对视频不做处理
}

// scheduleNextItem() 中
1 -> { // 视频
    Log.d(TAG, "调度视频播放")
    // ❌ 问题：这里什么都不做，视频不会重新播放
}
```

## ✅ 修复方案

### 1. 修改 handleItemCompleted() 逻辑

```kotlin
if (currentItem.type == 1) { // 视频
    Log.d(TAG, "只有一个视频，重新播放")
    // 使用专门的方法重新播放视频
    restartSingleVideo()
}
```

### 2. 添加 restartSingleVideo() 方法

```kotlin
private fun restartSingleVideo() {
    Log.d(TAG, "重新播放单个视频")
    // 使用适配器的方法强制重新播放视频
    adapter.restartVideoAt(0)
}
```

### 3. 在适配器中添加强制重播方法

```kotlin
fun restartVideoAt(position: Int) {
    Log.d(TAG, "强制重新播放视频，位置: $position")
    notifyItemChanged(position)
}
```

## 🔄 完整的单视频循环流程

```
1. 视频开始播放
   ↓
2. 视频播放完成 → onAdCompleted() 回调
   ↓
3. 调用 handleItemCompleted()
   ↓
4. 检测到只有一个视频
   ↓
5. 调用 restartSingleVideo()
   ↓
6. 调用 adapter.restartVideoAt(0)
   ↓
7. 适配器调用 notifyItemChanged(0)
   ↓
8. ViewHolder 重新绑定 → bind() 方法
   ↓
9. release() 释放旧播放器
   ↓
10. createNewPlayer() 创建新播放器
    ↓
11. 视频从头开始播放
    ↓
12. 回到步骤1，形成循环
```

## 🎯 关键点说明

### 1. 为什么不在 scheduleNextItem() 中处理？

`scheduleNextItem()` 的设计目的是"调度下一个素材的播放"，对于视频来说：
- 多视频场景：视频播放完成通过回调自动切换到下一个
- 单视频场景：需要重新播放当前视频，这不是"下一个"的概念

所以在 `scheduleNextItem()` 中处理单视频循环在语义上是不合适的。

### 2. 为什么使用 notifyItemChanged()？

```kotlin
adapter.restartVideoAt(0) → notifyItemChanged(0)
```

这会强制 RecyclerView 重新绑定指定位置的 ViewHolder，触发：
- `release()` 释放旧播放器
- `createNewPlayer()` 创建新播放器
- 视频从头开始播放

### 3. 为什么不直接调用播放器的 seekTo(0)？

虽然可以使用 `player.seekTo(0)` 让视频回到开头，但：
- 需要额外的播放器状态管理
- 可能存在播放器状态不一致的问题
- 重新创建播放器更简单可靠

## 🧪 测试验证

可以通过以下方式测试单视频循环：

```kotlin
// 创建只有一个视频的轮播数据
val singleVideoTask = RTAdTaskModel(
    taskId = "single_video_test",
    playType = 1,
    carouseAreaList = listOf(
        RTAdCarouseAreaModel(
            level = 1,
            left = 0, top = 0,
            width = 800, height = 600,
            carouseList = listOf(
                RTAdCarouseItemModel(
                    assetId = 1L,
                    localPath = "/path/to/video.mp4",
                    type = 1, // 视频
                    playtime = 10L
                )
            )
        )
    )
)

// 加载并开始播放
controller.loadAdTask(singleVideoTask)
controller.startAllCarousels()

// 预期行为：视频播放完成后自动重新开始播放
```

## 📝 日志验证

正确的日志输出应该是：

```
D/RTAdBannerView: 视频播放完成，位置: 0
D/RTAdBannerView: 只有一个视频，重新播放
D/RTAdBannerView: 重新播放单个视频
D/RTAdPagerAdapter: 强制重新播放视频，位置: 0
D/RTAdPagerAdapter: 绑定视频素材，位置: 0, 路径: /path/to/video.mp4
D/RTAdPagerAdapter: 创建新播放器，位置: 0
D/RTAdPagerAdapter: 视频开始播放，位置: 0
... (视频播放过程)
D/RTAdPagerAdapter: 视频播放完成，位置: 0
... (循环重复)
```

## ✅ 修复效果

- ✅ **单视频循环**：视频播放完成后自动重新开始
- ✅ **逻辑清晰**：专门的方法处理专门的场景
- ✅ **可靠性高**：通过适配器刷新确保重新创建播放器
- ✅ **易于调试**：清晰的日志输出便于问题排查
