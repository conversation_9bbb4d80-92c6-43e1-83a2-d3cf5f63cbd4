# RTAdSDK 轮播广告优化说明

## 🎯 优化目标

根据您的需求，实现了以下功能：

1. ✅ **视频重新播放**：轮播到视频时，视频从头开始播放
2. ✅ **单视频循环**：只有一个视频时，播放完成后重新从头播放
3. ✅ **单图片持续显示**：只有一张图片时，持续显示直到资源切换
4. ✅ **优雅数据切换**：数据切换时等待当前素材播放完成

## 🔧 核心优化

### 1. 视频播放策略优化

**RTAdPagerAdapter.kt 修改：**
```kotlin
// 每次绑定都重新创建播放器，确保视频从头播放
fun bind(item: RTAdCarouseItemModel, position: Int) {
    // 检查文件存在性
    val localFile = File(item.localPath)
    if (!localFile.exists()) {
        onItemPlayListener?.onVideoError(position, "视频文件不存在")
        return
    }

    // 每次都重新创建播放器，确保从头播放
    release()
    createNewPlayer(item, position)
}
```

**效果：**
- ✅ 每次轮播到视频都从头开始播放
- ✅ 避免了复杂的状态管理
- ✅ 确保播放的一致性

### 2. 单素材循环播放

**RTAdBannerView.kt 优化：**
```kotlin
private fun handleItemCompleted() {
    if (carouselItems.size == 1) {
        val currentItem = carouselItems[0]
        if (currentItem.type == 1) { // 视频
            Log.d(TAG, "只有一个视频，重新播放")
            viewPager.setCurrentItem(0, false)
            scheduleNextItem()
        } else { // 图片
            Log.d(TAG, "只有一张图片，持续显示")
            // 图片持续显示，不做任何操作
        }
    } else {
        // 多个素材，正常轮播
        moveToNextItem()
    }
}
```

**效果：**
- ✅ 单个视频：播放完成后重新开始
- ✅ 单张图片：持续显示不切换
- ✅ 多个素材：正常轮播

### 3. 优雅数据切换

**RTAdBannerView.kt 新增：**
```kotlin
fun setCarouselData(carouseAreaModel: RTAdCarouseAreaModel) {
    val newItems = carouseAreaModel.carouseList
    
    if (isCarouselRunning && carouselItems.isNotEmpty()) {
        // 如果正在播放，等待当前素材播放完成后切换
        pendingCarouselItems = newItems
        isPendingDataChange = true
    } else {
        // 直接切换数据
        applyNewCarouselData(newItems)
    }
}

private fun handleItemCompleted() {
    // 检查是否有待切换的数据
    if (isPendingDataChange && pendingCarouselItems != null) {
        Log.d(TAG, "当前素材播放完成，切换到新数据")
        applyNewCarouselData(pendingCarouselItems!!)
        viewPager.setCurrentItem(0, false)
        scheduleNextItem()
        return
    }
    // ... 其他逻辑
}
```

**效果：**
- ✅ 数据切换时不会立即中断当前播放
- ✅ 等待当前素材（视频/图片）播放完成
- ✅ 平滑过渡到新的轮播内容

## 📋 使用场景验证

### 场景1：单个视频循环
```kotlin
val videoItem = RTAdCarouseItemModel(
    assetId = 1L,
    localPath = "/path/to/video.mp4",
    type = 1, // 视频
    playtime = 10L
)

val carouselArea = RTAdCarouseAreaModel(
    carouseList = listOf(videoItem) // 只有一个视频
)
```
**预期行为：** 视频播放完成后自动重新开始播放

### 场景2：单张图片持续显示
```kotlin
val imageItem = RTAdCarouseItemModel(
    assetId = 2L,
    localPath = "/path/to/image.jpg",
    type = 0, // 图片
    playtime = 5L // 这个时长会被忽略
)

val carouselArea = RTAdCarouseAreaModel(
    carouseList = listOf(imageItem) // 只有一张图片
)
```
**预期行为：** 图片持续显示，不会切换

### 场景3：混合内容轮播
```kotlin
val items = listOf(
    imageItem,  // 图片，播放3秒
    videoItem,  // 视频，播放完成后切换
    imageItem2  // 图片，播放4秒
)
```
**预期行为：** 
- 图片显示3秒后切换到视频
- 视频从头播放，完成后切换到下一张图片
- 图片显示4秒后回到第一张图片
- 循环往复

### 场景4：数据切换
```kotlin
// 正在播放混合内容时
controller.loadAdTask(newAdTask) // 设置新数据

// 预期行为：
// 1. 当前素材继续播放完成
// 2. 播放完成后切换到新数据
// 3. 新数据从第一个素材开始播放
```

## 🔍 关键实现细节

### 1. 视频重新播放机制
- 每次 `bind()` 都调用 `release()` 释放旧播放器
- 创建新的播放器实例，确保从头播放
- 避免了播放状态的复用

### 2. 单素材检测
```kotlin
if (carouselItems.size == 1) {
    // 单素材逻辑
} else {
    // 多素材轮播逻辑
}
```

### 3. 数据切换状态管理
```kotlin
private var pendingCarouselItems: List<RTAdCarouseItemModel>? = null
private var isPendingDataChange = false
```

### 4. 统一的完成处理
```kotlin
private fun handleItemCompleted() {
    // 统一处理图片时长到期和视频播放完成
}
```

## 🧪 测试验证

提供了 `RTAdCarouselTestActivity` 用于测试各种场景：

1. **单视频测试**：验证视频循环播放
2. **单图片测试**：验证图片持续显示
3. **混合内容测试**：验证图片+视频轮播
4. **数据切换测试**：验证优雅的数据切换

## 📝 使用建议

1. **文件路径**：确保 `localPath` 指向的文件存在
2. **播放时长**：图片的 `playtime` 单位为秒
3. **生命周期**：正确处理Activity的生命周期
4. **错误处理**：监听播放错误并适当处理

## 🎉 优化效果

- ✅ **用户体验**：视频每次都从头播放，符合预期
- ✅ **资源管理**：正确的播放器创建和释放
- ✅ **播放控制**：灵活的单素材和多素材处理
- ✅ **数据切换**：平滑的内容更新，不中断当前播放
- ✅ **代码简洁**：移除了复杂的状态管理，逻辑更清晰
