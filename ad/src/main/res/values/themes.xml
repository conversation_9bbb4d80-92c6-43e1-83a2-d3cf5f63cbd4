<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.RTNewPos" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <!-- <item name="colorPrimary">@color/my_light_primary</item> -->
    </style>

    <style name="Theme.RTNewPos" parent="Base.Theme.RTNewPos" />

    <style name="FullScreen" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentNavigation" tools:ignore="NewApi">true</item>
        <!--解决部分手机隐藏状态栏顶部出现小黑条的问题-->
        <item name="android:windowLayoutInDisplayCutoutMode" tools:ignore="NewApi">shortEdges</item>
        <!--解决白屏问题-->
        <!--Android 5.x开始需要把颜色设置透明，否则导航栏会呈现系统默认的浅灰色-->
        <item name="android:statusBarColor" tools:ignore="NewApi">#00000000</item>
<!--        <item name="android:windowBackground">@drawable/ic_splash_bg</item>-->
    </style>
</resources>