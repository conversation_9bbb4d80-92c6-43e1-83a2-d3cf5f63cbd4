# RTAdSDK 轮播广告功能使用说明

## 概述

RTAdSDK 现在支持轮播广告功能，可以播放图片和视频的混合资源。轮播功能支持多个轮播区域同时播放，每个区域可以独立配置位置、大小和素材列表。

## 功能特性

- ✅ 支持图片+视频混合轮播
- ✅ 支持多个轮播区域同时播放
- ✅ 图片播放时长由 `playtime` 字段控制
- ✅ 视频播放完成后自动切换
- ✅ 列表播放完成后自动重新开始轮播
- ✅ 只播放本地资源（使用 `localPath` 字段）
- ✅ 支持暂停、恢复、停止操作

## 数据模型

### RTAdTaskModel
```kotlin
data class RTAdTaskModel(
    val taskId: String,
    val playType: Int,
    val carouseAreaList: List<RTAdCarouseAreaModel>, // 轮播区域列表
    val fixedAreaList: List<RTFixedAreaModel>? = null
)
```

### RTAdCarouseAreaModel
```kotlin
data class RTAdCarouseAreaModel(
    val level: Int,     // 素材层级
    val left: Int,      // 左上角X坐标
    val top: Int,       // 左上角Y坐标
    val width: Int,     // 元素宽度
    val height: Int,    // 元素高度
    val carouseList: List<RTAdCarouseItemModel> // 素材列表
)
```

### RTAdCarouseItemModel
```kotlin
data class RTAdCarouseItemModel(
    val assetId: Long,
    val url: String,
    val localPath: String,  // 本地文件路径（重要）
    val size: Long,
    val type: Int,          // 0-图片, 1-视频
    val playtime: Long,     // 播放时长（秒）
    val times: Int
)
```

## 使用方法

### 1. 基本使用

```kotlin
// 在Activity中
class MainActivity : AppCompatActivity() {
    private lateinit var containerView: FrameLayout
    private lateinit var carouselController: RTAdCarouselController

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        // 初始化容器视图
        containerView = findViewById(R.id.adContainer)
        
        // 创建轮播控制器
        carouselController = RTAdCarouselController(this)
        carouselController.setContainerView(containerView)
        
        // 设置到管理器
        RTAdsManager.setCarouselController(carouselController)
        
        // 加载广告任务（从数据库或网络获取）
        loadAdTask()
    }
    
    private fun loadAdTask() {
        // 假设从某处获取到广告任务数据
        val adTask = getAdTaskFromDatabase() // 或其他数据源
        
        // 验证数据有效性
        if (RTAdCarouselHelper.validateAdTask(adTask)) {
            carouselController.loadAdTask(adTask)
            RTAdsManager.startCarouselAd()
        }
    }
    
    override fun onResume() {
        super.onResume()
        RTAdsManager.resumeCarouselAd()
    }
    
    override fun onPause() {
        super.onPause()
        RTAdsManager.pauseCarouselAd()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        carouselController.release()
    }
}
```

### 2. 使用便捷方法

```kotlin
// 使用RTAdCarouselHelper创建控制器
val controller = RTAdCarouselHelper.createCarouselController(
    context = this,
    containerView = containerView,
    adTask = adTask
)

controller?.let {
    RTAdsManager.setCarouselController(it)
    RTAdsManager.startCarouselAd()
}
```

### 3. 直接使用RTAdCarouselActivity

```kotlin
// 继承RTAdCarouselActivity
class MyAdActivity : RTAdCarouselActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取广告任务数据
        val adTask = getAdTaskData()
        
        // 加载广告任务
        loadAdTask(adTask)
    }
    
    private fun getAdTaskData(): RTAdTaskModel {
        // 从Intent、数据库或其他地方获取数据
        return RTAdTaskModel(...)
    }
}
```

## 布局文件

### activity_main.xml
```xml
<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/adContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">
    
    <!-- 轮播广告将动态添加到这里 -->
    
</FrameLayout>
```

## API 参考

### RTAdsManager 轮播相关方法

```kotlin
// 设置轮播控制器
RTAdsManager.setCarouselController(controller: RTAdCarouselController)

// 开始轮播
RTAdsManager.startCarouselAd()

// 停止轮播
RTAdsManager.stopCarouselAd()

// 暂停轮播
RTAdsManager.pauseCarouselAd()

// 恢复轮播
RTAdsManager.resumeCarouselAd()
```

### RTAdCarouselController 方法

```kotlin
// 设置容器视图
controller.setContainerView(container: FrameLayout)

// 加载广告任务
controller.loadAdTask(adTask: RTAdTaskModel)

// 控制所有轮播
controller.startAllCarousels()
controller.stopAllCarousels()
controller.pauseAllCarousels()
controller.resumeAllCarousels()

// 释放资源
controller.release()
```

### RTAdCarouselHelper 工具方法

```kotlin
// 验证广告任务
RTAdCarouselHelper.validateAdTask(adTask: RTAdTaskModel): Boolean

// 创建控制器
RTAdCarouselHelper.createCarouselController(
    context: Context,
    containerView: FrameLayout,
    adTask: RTAdTaskModel
): RTAdCarouselController?

// 工具方法
RTAdCarouselHelper.getAssetTypeDescription(type: Int): String
RTAdCarouselHelper.formatPlaytime(playtime: Long): String
RTAdCarouselHelper.isSupportedImageFormat(filePath: String): Boolean
RTAdCarouselHelper.isSupportedVideoFormat(filePath: String): Boolean
RTAdCarouselHelper.printAdTaskInfo(adTask: RTAdTaskModel)
```

## 注意事项

1. **本地文件路径**：确保 `RTAdCarouseItemModel.localPath` 指向的文件存在
2. **播放时长**：图片的 `playtime` 字段单位为秒
3. **文件格式**：支持常见的图片格式（jpg, png, gif等）和视频格式（mp4, avi等）
4. **内存管理**：记得在适当的时候调用 `release()` 方法释放资源
5. **生命周期**：在Activity的生命周期方法中正确处理轮播的暂停和恢复

## 调试

使用 `RTAdCarouselHelper.printAdTaskInfo(adTask)` 可以打印详细的广告任务信息，便于调试。

日志标签：
- `RTAdBannerView`：单个轮播视图的日志
- `RTAdPagerAdapter`：适配器相关日志
- `RTAdCarouselController`：控制器日志
- `RTAdCarouselHelper`：工具类日志
