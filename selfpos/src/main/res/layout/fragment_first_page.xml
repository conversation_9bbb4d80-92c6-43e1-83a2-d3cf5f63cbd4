<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clEdpLoginRoot"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/vwHideFunctionView"
        android:layout_width="@dimen/dp_120"
        android:layout_height="@dimen/dp_80"
        android:background="#00000000"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.rtmart.device.rtViews.RTScanEditText
        android:id="@+id/et_number"
        android:layout_width="@dimen/dp_1"
        android:layout_height="@dimen/dp_1"
        android:layout_marginHorizontal="@dimen/dp_17"
        android:layout_marginTop="@dimen/dp_34"
        android:background="@drawable/shape_common_edit_red_bg"
        android:hint="请输入商品码"
        android:imeOptions="flagNoFullscreen"
        android:inputType="text"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_10"
        android:textColor="@color/lightBlack"
        android:textColorHint="@color/gray_999"
        android:textSize="@dimen/sp_16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clCashierHome"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_cart"
        android:visibility="visible">

        <ImageView
            android:id="@+id/imgRtMartView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_21"
            android:layout_marginTop="@dimen/dp_26"
            android:background="@drawable/rtmart"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/tvRePrint"
            android:layout_width="@dimen/dp_67"
            android:layout_height="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_20"
            android:background="@drawable/shape_logout_btn"
            android:gravity="center"
            android:text="补打小票"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_10"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvWelcomeTip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_158"
            android:gravity="center"
            android:text="欢迎使用自助收银"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_27"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.rtmart.core.view.QRCodeView
            android:id="@+id/qrMemberRegister"
            android:layout_width="@dimen/dp_107"
            android:layout_height="@dimen/dp_107"
            android:layout_marginTop="@dimen/dp_67"
            android:background="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvWelcomeTip"/>

        <TextView
            android:id="@+id/tvQRMemberRegisterTip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="微信、支付宝扫码登录/注册"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_18"
            android:layout_marginTop="@dimen/dp_14"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/qrMemberRegister" />

        <TextView
            android:id="@+id/tvPhoneRegister"
            android:layout_width="@dimen/dp_220"
            android:layout_height="@dimen/dp_50"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp_60"
            android:background="@drawable/selector_btn_common_radius_17"
            android:enabled="true"
            android:gravity="center"
            android:text="手机号登录/注册"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_18"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvQRMemberRegisterTip" />

        <TextView
            android:id="@+id/tvAbandonMember"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="放弃会员优惠，直接结账"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            android:layout_marginTop="@dimen/dp_20"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvPhoneRegister" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="@dimen/dp_230"
            android:layout_height="@dimen/dp_160"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/shape_dialog"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvWelcomeTip">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:gravity="center"
                android:text="收银员未上机"
                android:textColor="@color/lightBlack"
                android:textSize="@dimen/sp_29"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_20"
                android:gravity="center"
                android:paddingBottom="@dimen/dp_30"
                android:text="请扫上机码完成上机"
                android:textColor="@color/lightBlack"
                android:textSize="@dimen/sp_20"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_title" />
        </androidx.constraintlayout.widget.ConstraintLayout>

<!--        <ImageView-->
<!--            android:id="@+id/tvRTPOS"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginBottom="@dimen/dp_22"-->
<!--            android:background="@drawable/copyright"-->
<!--            android:textColor="@color/gray_999"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="parent" />-->

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clADPage"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        tools:visibility="gone"
        android:visibility="gone">

<!--        <com.kiwi.adsdk.widgets.AdInfoView-->
<!--            android:id="@+id/adView"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent" />-->
        <FrameLayout
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <TextView
            android:id="@+id/tvADPhoneRegister"
            android:layout_width="@dimen/dp_147"
            android:layout_height="@dimen/dp_50"
            android:layout_gravity="center"
            android:layout_marginBottom="@dimen/dp_55"
            android:background="@drawable/selector_btn_common_radius_17"
            android:enabled="true"
            android:gravity="center"
            android:text="手机号登录/注册"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/tvADAbandonMember" />

        <com.rtmart.core.view.QRCodeView
            android:id="@+id/qrADMemberRegister"
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_60"
            android:layout_marginBottom="@dimen/dp_51"
            android:background="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/tvQRMemberRegisterTip1"
            app:layout_constraintStart_toStartOf="@+id/tvQRMemberRegisterTip1"/>

        <TextView
            android:id="@+id/tvQRMemberRegisterTip1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="微信支付宝扫码登录/注册"
            android:textColor="@color/lightBlack"
            android:textSize="@dimen/sp_12"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginStart="@dimen/dp_25"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/qrADMemberRegister" />

        <TextView
            android:id="@+id/tvADAbandonMember"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="放弃会员优惠，直接结账"
            android:textColor="@color/lightBlack"
            android:textSize="@dimen/sp_12"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_27"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvADPhoneRegister" />

        <TextView
            android:id="@+id/tvAdReprint"
            android:layout_width="@dimen/dp_67"
            android:layout_height="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_20"
            android:background="@drawable/shape_gray_reprint_btn"
            android:gravity="center"
            android:text="补打小票"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:id="@+id/cl_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_5">

        <LinearLayout
            android:id="@+id/llInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_7"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/tvRTPOS"
                android:layout_width="@dimen/dp_35"
                android:layout_height="@dimen/dp_10"
                android:background="@drawable/copyright_gray"
                android:textColor="@color/gray_999" />

            <TextView
                android:id="@+id/tvVersion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_7"
                android:text="V1.5.0"
                android:textColor="#ff999999"
                android:textSize="@dimen/sp_9" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_7"
                android:text="201"
                android:id="@+id/tvPosNo"
                android:textAlignment="center"
                android:textColor="#ff999999"
                android:textSize="@dimen/sp_9" />

            <TextView
                android:id="@+id/tvDeviceNo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_7"
                android:text="989db1e7-4db1-387a-990e-6c502f654faa"
                android:textColor="#ff999999"
                android:textSize="@dimen/sp_9" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>